# Repository Guidelines

## Project Structure & Module Organization
- Entry: `src/main.tsx` boots the React 19 Vite app and imports shared styles from `src/index.css`.
- Routes: scope feature screens to `src/routes/`; keep navigation glue within each route folder.
- UI: house reusable components in `src/components/`, colocated hooks in `src/hooks/`, and Zustand stores in `src/stores/`.
- Services: expose Comlink workers from `src/workers/`; place shared helpers in `src/lib/` and configuration in `src/config/`.
- Assets: keep generated assets in `src/assets/` and ship static files from `public/`. Update `README.md` diagrams when adding top-level folders.

## Build, Test, and Development Commands
- `pnpm dev` — start the Vite 7 dev server with HMR.
- `pnpm build` — run `tsc -b` and emit the production bundle.
- `pnpm preview` — serve the last build; use `pnpm preview:build` to rebuild first.
- `pnpm lint`, `pnpm format:check`, `pnpm check` — run Biome linting, formatting verification, and the combined lint/type pass. Use `:fix` variants only when the diff is reviewable.
- `pnpm build:analyze` — inspect bundle makeup when tracing size issues.

## Coding Style & Naming Conventions
Biome enforces 2-space indentation, 80-character lines, and single quotes in TS/JS (double quotes in JSX). Order imports as React → third-party → `@/...` aliases → relative paths, marking type-only imports with `type`. Components use PascalCase, functions and variables camelCase, filenames kebab-case. Keep Tailwind classes atomic and mirror dark/light variants (e.g., `bg-muted dark:bg-muted/80`). Declare explicit TypeScript types for every exported API.

## Testing Guidelines
Automated tests are not wired yet, so run `pnpm check` before every PR to catch lint and type regressions. Document manual smoke tests for critical flows—route navigation, advanced search, and the image dialog—in the PR description or attach a brief recording. Colocate domain helpers beside the feature and name them `<feature>.test-helper.ts`.

## Commit & Pull Request Guidelines
Use Conventional Commits (`type(scope): summary`), for example `feat(routes): add gallery filters`. Rebase before pushing, list executed commands (at minimum `pnpm check`), and link relevant issues. Provide screenshots or clips for UI changes and call out edits to stores, workers, or configuration so reviewers can assess side effects.

## Security & Configuration Tips
Expose browser-readable secrets only with the `VITE_` prefix. Restrict configuration tweaks to `vite.config.ts`, `tailwind.config.ts`, or files inside `src/config/`. Persist user preferences through clearly named Zustand actions to keep storage keys discoverable. Avoid destructive git commands unless requested, and do not downgrade React 19 or Vite 7 without team consensus.
