{"name": "demo-ai", "private": true, "version": "0.1.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:analyze": "tsc -b && vite build --mode analyze", "lint": "biome lint ./src", "lint:fix": "biome lint ./src --write", "format": "biome format ./src --write", "format:check": "biome format ./src", "check": "biome check ./src", "check:fix": "biome check ./src --write", "preview": "vite preview", "preview:build": "pnpm build && pnpm preview"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@xyflow/react": "^12.8.6", "ahooks": "^3.9.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "comlink": "^4.4.2", "date-fns": "^4.1.0", "dayjs": "^1.11.18", "dom-to-image": "^2.6.0", "embla-carousel-react": "^8.6.0", "file-saver": "^2.0.5", "idb-keyval": "^6.2.2", "input-otp": "^1.4.2", "ky": "^1.11.0", "lodash": "^4.17.21", "lucide-react": "^0.451.0", "next-themes": "^0.4.6", "radash": "^12.1.1", "react": "^19.2.0", "react-color": "^2.19.3", "react-day-picker": "9.7.0", "react-dom": "^19.2.0", "react-hook-form": "^7.64.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "react-router": "^7.9.3", "recharts": "^2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@tailwindcss/typography": "^0.5.19", "@types/dom-to-image": "^2.6.7", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.20", "@types/node": "^22.18.8", "@types/react": "^19.2.2", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.2.1", "@vitejs/plugin-react-swc": "^4.1.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.18", "typescript": "^5.9.3", "vite": "^7.1.9", "vite-plugin-comlink": "^5.3.0"}, "packageManager": "pnpm@10.17.0", "pnpm": {"ignoredBuiltDependencies": ["@swc/core"], "onlyBuiltDependencies": ["@anthropic-ai/claude-code", "@swc/core", "esbuild"]}}