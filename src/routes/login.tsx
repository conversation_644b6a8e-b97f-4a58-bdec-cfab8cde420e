import { useMount } from 'ahooks'
import { useCommonStore } from '@/stores/common'
import LoginBody from '../components/login-body'

export function HydrateFallback() {
  return <div>Loading...</div>
}

export default function Login() {
  const { resetDefault } = useCommonStore()
  useMount(() => {
    resetDefault()
  })

  return (
    <div className="flex h-full overflow-y-auto flex-col overscroll-none">
      <LoginBody />
    </div>
  )
}
