import { useDebounceFn, useUpdateEffect } from 'ahooks'
import { useMemo } from 'react'
import {
  commonAspectRatios,
  INPUT_SETTING_DEFAULT,
  type InputSettingType,
  versionList,
} from '@/config/index'
import { cn } from '@/lib/utils'
import type { GenerationType } from '@/stores/creation'
import { Button } from './ui/button'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from './ui/select'
import { Slider } from './ui/slider'

type InputSettingProps = {
  data: InputSettingType
  onChange: (setting: Partial<InputSettingType>) => void
  onSave: () => void
  type?: GenerationType
}
export const InputSetting = ({
  data,
  type = 'image',
  onChange,
  onSave,
}: InputSettingProps) => {
  const [costomInputSetting, setCostomInputSetting] = [data, onChange]
  const isVideoType = useMemo(() => {
    return type === 'video'
  }, [type])
  const aspectRatioInfo = useMemo(() => {
    return isVideoType
      ? commonAspectRatios[5]
      : commonAspectRatios[costomInputSetting.aspect] || commonAspectRatios[5]
  }, [costomInputSetting.aspect, isVideoType])
  const aestheticsInfo = useMemo(() => {
    return {
      stylize: costomInputSetting.stylize ?? INPUT_SETTING_DEFAULT.stylize,
      weird: costomInputSetting.weird ?? INPUT_SETTING_DEFAULT.weird,
      chaos: costomInputSetting.chaos ?? INPUT_SETTING_DEFAULT.chaos,
    }
  }, [
    costomInputSetting.stylize,
    costomInputSetting.weird,
    costomInputSetting.chaos,
  ])
  const aestheticsIsChange = useMemo(() => {
    return (
      aestheticsInfo.stylize !== INPUT_SETTING_DEFAULT.stylize ||
      aestheticsInfo.weird !== INPUT_SETTING_DEFAULT.weird ||
      aestheticsInfo.chaos !== INPUT_SETTING_DEFAULT.chaos
    )
  }, [aestheticsInfo])
  const modelIsChange = useMemo(() => {
    return (
      costomInputSetting.version !== INPUT_SETTING_DEFAULT.version ||
      costomInputSetting.draft !== INPUT_SETTING_DEFAULT.draft ||
      costomInputSetting.raw !== INPUT_SETTING_DEFAULT.raw
    )
  }, [
    costomInputSetting.version,
    costomInputSetting.draft,
    costomInputSetting.raw,
  ])
  const otherIsChange = useMemo(() => {
    return (
      costomInputSetting.fast !== INPUT_SETTING_DEFAULT.fast ||
      costomInputSetting.video_type !== INPUT_SETTING_DEFAULT.video_type ||
      costomInputSetting.batch_size !== INPUT_SETTING_DEFAULT.batch_size
    )
  }, [
    costomInputSetting.fast,
    costomInputSetting.video_type,
    costomInputSetting.batch_size,
  ])
  const modelVersion = useMemo(() => {
    return isVideoType
      ? INPUT_SETTING_DEFAULT.version
      : (costomInputSetting.version ?? INPUT_SETTING_DEFAULT.version)
  }, [costomInputSetting.version, isVideoType])
  const { run } = useDebounceFn(onSave)

  // 数据同步
  useUpdateEffect(() => {
    run()
  }, [costomInputSetting])

  return (
    <div className="w-full grid grid-cols-2 gap-3 pl-14 2xl:pl-0">
      <div
        className={cn('relative border rounded-md p-4 bg-muted/50', {
          'pointer-events-none opacity-30 cursor-not-allowed': isVideoType,
        })}
      >
        {costomInputSetting.aspect !== INPUT_SETTING_DEFAULT.aspect && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-1 right-2 font-light"
            onClick={() =>
              setCostomInputSetting({ aspect: INPUT_SETTING_DEFAULT.aspect })
            }
          >
            重置
          </Button>
        )}
        <p className="text-center text-sm font-medium mb-2">图像比例</p>
        <div className="relative flex gap-2 items-center">
          <div className="relative size-32">
            <div
              className={cn(
                'max-w-full max-h-full w-auto h-auto z-10 bg-background text-sm rounded-md border-2 border-muted-foreground flex justify-center items-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
                {
                  'w-full': aspectRatioInfo.value >= 1,
                  'h-full': aspectRatioInfo.value < 1,
                }
              )}
              style={{
                aspectRatio: aspectRatioInfo.value,
              }}
            >
              {aspectRatioInfo.name}
            </div>
            {costomInputSetting.aspect !== INPUT_SETTING_DEFAULT.aspect && (
              <div
                className={cn(
                  'max-w-full max-h-full w-auto h-auto bg-background text-sm rounded-md border-2 border-muted-foreground/50 border-dashed flex justify-center items-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
                  {
                    'h-full': aspectRatioInfo.value >= 1,
                    'w-full': aspectRatioInfo.value < 1,
                  }
                )}
                style={{
                  aspectRatio: 1 / aspectRatioInfo.value,
                }}
              />
            )}
          </div>
          <div className="flex-1 p-4">
            <div className="py-4 flex justify-center border-b">
              <Button
                variant={
                  costomInputSetting.aspect < 5 && !isVideoType
                    ? 'default'
                    : 'outline'
                }
                size="sm"
                className="rounded-r-none"
                onClick={() => setCostomInputSetting({ aspect: 3 })}
              >
                纵向
              </Button>
              <Button
                variant={
                  costomInputSetting.aspect === 5 || isVideoType
                    ? 'default'
                    : 'outline'
                }
                size="sm"
                className="rounded-none !border-x-transparent"
                onClick={() => setCostomInputSetting({ aspect: 5 })}
              >
                正方形
              </Button>
              <Button
                variant={
                  costomInputSetting.aspect > 5 && !isVideoType
                    ? 'default'
                    : 'outline'
                }
                size="sm"
                className="rounded-l-none"
                onClick={() => setCostomInputSetting({ aspect: 7 })}
              >
                横向
              </Button>
            </div>
            <div className="w-full py-6">
              <Slider
                value={[
                  isVideoType
                    ? INPUT_SETTING_DEFAULT.aspect
                    : costomInputSetting.aspect,
                ]}
                max={10}
                step={1}
                min={0}
                className="w-full"
                onValueChange={value =>
                  setCostomInputSetting({ aspect: value[0] })
                }
              />
            </div>
          </div>
        </div>
      </div>
      <div
        className={cn('relative border rounded-md p-4 bg-muted/50', {
          'pointer-events-none opacity-30 cursor-not-allowed': isVideoType,
        })}
      >
        {aestheticsIsChange && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-1 right-2 font-light"
            onClick={() =>
              setCostomInputSetting({
                stylize: INPUT_SETTING_DEFAULT.stylize,
                weird: INPUT_SETTING_DEFAULT.weird,
                chaos: INPUT_SETTING_DEFAULT.chaos,
              })
            }
          >
            重置
          </Button>
        )}
        <p className="text-center text-sm font-medium mb-2">美学设置</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              风格化
            </p>
            <div className="flex-1">
              <Slider
                value={[
                  isVideoType
                    ? INPUT_SETTING_DEFAULT.stylize
                    : aestheticsInfo.stylize,
                ]}
                max={1000}
                step={50}
                min={0}
                className="w-full"
                onValueChange={value =>
                  setCostomInputSetting({ stylize: value[0] })
                }
              />
            </div>
          </div>
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              怪异化
            </p>
            <div className="flex-1">
              <Slider
                value={[
                  isVideoType
                    ? INPUT_SETTING_DEFAULT.weird
                    : aestheticsInfo.weird,
                ]}
                max={3000}
                step={100}
                min={0}
                className="w-full"
                onValueChange={value =>
                  setCostomInputSetting({ weird: value[0] })
                }
              />
            </div>
          </div>
          <div className="flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              多样化
            </p>
            <div className="flex-1">
              <Slider
                value={[
                  isVideoType
                    ? INPUT_SETTING_DEFAULT.chaos
                    : aestheticsInfo.chaos,
                ]}
                max={100}
                step={10}
                min={0}
                className="w-full"
                onValueChange={value =>
                  setCostomInputSetting({ chaos: value[0] })
                }
              />
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-4 bg-muted/50">
        {modelIsChange && (
          <Button
            variant="ghost"
            size="sm"
            className={cn('absolute top-1 right-2 font-light', {
              'pointer-events-none opacity-30 cursor-not-allowed': isVideoType,
            })}
            onClick={() =>
              setCostomInputSetting({
                version: INPUT_SETTING_DEFAULT.version,
                draft: INPUT_SETTING_DEFAULT.draft,
                raw: INPUT_SETTING_DEFAULT.raw,
              })
            }
          >
            重置
          </Button>
        )}
        <p className="text-center text-sm font-medium mb-2">模型</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              模式
            </p>
            <div className="flex-1 flex justify-end">
              <Button
                variant={costomInputSetting.raw ? 'outline' : 'default'}
                size="sm"
                className="rounded-r-none"
                onClick={() => setCostomInputSetting({ raw: 0 })}
              >
                标准
              </Button>
              <Button
                variant={costomInputSetting.raw ? 'default' : 'outline'}
                size="sm"
                className="rounded-l-none"
                onClick={() => setCostomInputSetting({ raw: 1 })}
              >
                原始
              </Button>
            </div>
          </div>
          <div
            className={cn('flex gap-2 items-center py-4', {
              'pointer-events-none opacity-30 cursor-not-allowed': isVideoType,
            })}
          >
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              版本
            </p>
            <div className="flex-1 flex justify-end gap-2 items-center">
              {modelVersion === '7' && (
                <div className="flex-1 flex justify-end">
                  <Button
                    variant={costomInputSetting.draft ? 'outline' : 'default'}
                    size="sm"
                    className="rounded-r-none"
                    onClick={() => setCostomInputSetting({ draft: 0 })}
                  >
                    标准
                  </Button>
                  <Button
                    variant={costomInputSetting.draft ? 'default' : 'outline'}
                    size="sm"
                    className="rounded-l-none"
                    onClick={() => setCostomInputSetting({ draft: 1 })}
                  >
                    速写
                  </Button>
                </div>
              )}
              <Select
                value={modelVersion}
                onValueChange={value => {
                  setCostomInputSetting({ version: value })

                  if (value !== '7') {
                    setCostomInputSetting({ draft: 0 })
                  }
                }}
              >
                <SelectTrigger className="max-w-24 h-8">
                  <SelectValue placeholder="选择版本" />
                </SelectTrigger>
                <SelectContent align="end" className="w-24">
                  <SelectGroup>
                    <SelectLabel className="font-light hidden">
                      版本
                    </SelectLabel>
                    {versionList.map(({ id, name }) => (
                      <SelectItem
                        key={id}
                        value={id}
                        className="cursor-pointer"
                      >
                        {name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                  <SelectGroup>
                    <SelectLabel className="font-light text-xs text-muted-foreground select-none">
                      插画风格模型
                    </SelectLabel>
                    <SelectItem value="niji6" className="cursor-pointer">
                      niji 6
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-4 bg-muted/50">
        {otherIsChange && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-1 right-2 font-light"
            onClick={() =>
              setCostomInputSetting({
                fast: INPUT_SETTING_DEFAULT.fast,
                relax: INPUT_SETTING_DEFAULT.relax,
                turbo: INPUT_SETTING_DEFAULT.turbo,
                video_type: INPUT_SETTING_DEFAULT.video_type,
                batch_size: INPUT_SETTING_DEFAULT.batch_size,
              })
            }
          >
            重置
          </Button>
        )}
        <p className="text-center text-sm font-medium mb-2">更多选项</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              速度
            </p>
            <div className="flex-1 flex justify-end">
              {/* <Button
                variant={costomInputSetting.relax ? 'default' : 'outline'}
                size="sm"
                className="rounded-r-none"
                onClick={() =>
                  setCostomInputSetting({
                    relax: 1,
                    fast: 0,
                    turbo: 0,
                  })
                }
              >
                慢速
              </Button> */}
              <Button
                variant={
                  costomInputSetting.fast || isVideoType ? 'default' : 'outline'
                }
                size="sm"
                // className="rounded-none !border-x-transparent"
                className="rounded-r-none"
                onClick={() =>
                  setCostomInputSetting({
                    fast: 1,
                    relax: 0,
                    turbo: 0,
                  })
                }
              >
                快速
              </Button>
              <Button
                variant={
                  costomInputSetting.turbo && !isVideoType
                    ? 'default'
                    : 'outline'
                }
                size="sm"
                className={cn('rounded-l-none', {
                  'pointer-events-none opacity-30 cursor-not-allowed':
                    isVideoType,
                })}
                onClick={() =>
                  setCostomInputSetting({
                    turbo: 1,
                    fast: 0,
                    relax: 0,
                  })
                }
              >
                极速
              </Button>
            </div>
          </div>
          {/* <div className="flex gap-2 items-center py-4 border-b">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              隐身
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="default" size="sm" className="rounded-r-none">
                开
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none"
              >
                关
              </Button>
            </div>
          </div> */}
          <div className="flex gap-2 items-center py-4 border-b">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              视频清晰度
            </p>
            <div className="flex-1 flex justify-end">
              <Button
                variant={
                  costomInputSetting.video_type === 0 ? 'default' : 'outline'
                }
                size="sm"
                className="rounded-r-none"
                onClick={() => setCostomInputSetting({ video_type: 0 })}
              >
                SD
              </Button>
              <Button
                variant={
                  costomInputSetting.video_type === 1 ? 'default' : 'outline'
                }
                size="sm"
                className="rounded-l-none"
                onClick={() => setCostomInputSetting({ video_type: 1 })}
              >
                HD
              </Button>
            </div>
          </div>
          <div className="flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              视频个数
            </p>
            <div className="flex-1 flex justify-end">
              <Button
                variant={
                  costomInputSetting.batch_size === 1 ? 'default' : 'outline'
                }
                size="sm"
                className="rounded-r-none"
                onClick={() => setCostomInputSetting({ batch_size: 1 })}
              >
                1
              </Button>
              <Button
                variant={
                  costomInputSetting.batch_size === 2 ? 'default' : 'outline'
                }
                size="sm"
                className="rounded-none !border-x-transparent"
                onClick={() => setCostomInputSetting({ batch_size: 2 })}
              >
                2
              </Button>
              <Button
                variant={
                  costomInputSetting.batch_size === 4 ? 'default' : 'outline'
                }
                size="sm"
                className="rounded-l-none"
                onClick={() => setCostomInputSetting({ batch_size: 4 })}
              >
                4
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
