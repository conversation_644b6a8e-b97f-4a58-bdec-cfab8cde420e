import { Activity, useMemo } from 'react'
import type { PromptGenerationCommand } from '@/lib/utils'

export type ValueType =
  | string
  | number
  | boolean
  | { width: number; height: number }
type CreationParamsTagProps = {
  data: PromptGenerationCommand | undefined
  onTag: (key: string, value: ValueType) => void
}
export const getPromptParamsKeyQuickParse = (p: string) => {
  switch (p) {
    case 'aspectRatio':
      return 'ar'
    case 'modelVersion':
      return 'v'
    case 'styleStrength':
      return 's'
    case 'chaos':
      return 'c'
    case 'negativePrompt':
      return 'no'
    case 'quality':
      return 'q'
    case 'rawMode':
      return 'raw'
    case 'weird':
      return 'w'
    case 'imageWeight':
      return 'iw'
    case 'profile':
      return 'p'
    case 'repeat':
      return 'r'
    case 'experimental':
      return 'exp'
    case 'fastMode':
      return 'fast'
    case 'relaxMode':
      return 'relax'
    case 'turboMode':
      return 'turbo'
    case 'batchSize':
      return 'bs'
    case 'endFrameUrl':
      return 'end'

    default:
      return p
  }
}
export default function CreationParamsTag({
  data,
  onTag,
}: CreationParamsTagProps) {
  // 提示词参数数组数据，把 data 转为数组
  const promptParamsArray = useMemo(() => {
    if (!data) {
      return []
    }
    const result: {
      key: string
      value: ValueType
    }[] = []
    for (const [key, value] of Object.entries(data)) {
      if (
        key !== 'prompt' &&
        key !== 'contentUrls' &&
        key !== 'styleUrls' &&
        key !== 'characterUrls' &&
        key !== 'objectReferences' &&
        key !== 'objectWeights'
      ) {
        result.push({ key, value })
      }
    }
    return result
  }, [data])

  return (
    <Activity mode={promptParamsArray.length ? 'visible' : 'hidden'}>
      <div className="flex flex-wrap gap-1">
        {promptParamsArray.map(({ key, value }) => (
          <button
            type="button"
            className="flex gap-1 rounded-md border py-0.5 text-xs transition-colors focus:outline-none text-muted-foreground bg-muted font-light px-1 w-auto"
            key={key}
            onClick={() => onTag(key, value)}
          >
            <span>{getPromptParamsKeyQuickParse(key)}</span>
            <Activity mode={typeof value === 'boolean' ? 'hidden' : 'visible'}>
              {typeof value === 'object'
                ? `${value.width}:${value.height}`
                : String(value)}
            </Activity>
          </button>
        ))}
      </div>
    </Activity>
  )
}
