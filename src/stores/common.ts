import { toast } from 'sonner'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import { setLocalToken, webapi } from '@/lib/utils'
import type { ResponseType } from './creation'

type UserType = {
  id: number
  company_id: number | null
  use_company_package: number
  name: string
  email: string
  email_verified_at: string | null
  created_at: string
  updated_at: string
  nick_name: string
  real_name: string
  company: string
  company_desc: string
}

type CreditType = {
  id: number
  user_id: number
  name: string
  recharge_amount: number
  balance: number
  total_spent: number
  max_concurrent: number
  unit_price: number
  state: number
  start_at: string
  end_at: string
  created_at: string
  updated_at: string
  package_type: string
  time: string
}

type State = {
  loading: boolean
  user: UserType | null
  credit: CreditType | null
  creditLoading: boolean
  userLoading: boolean
}

type Actions = {
  setLoading: (loading: boolean) => void
  setUser: (user: UserType | null) => void
  logout: () => void
  login: (name: string, password: string) => Promise<void>
  changePassword: (oldPassword: string, newPassword: string) => void
  getUserInfo: () => void
  getCredits: () => void
  resetDefault: () => void
}

export const useCommonStore = create<State & Actions>()(
  persist(
    (set, get) => ({
      loading: false,
      creditLoading: false,
      userLoading: false,
      user: null,
      credit: null,
      resetDefault: () => {
        set({
          user: null,
          credit: null,
          loading: false,
          creditLoading: false,
          userLoading: false,
        })
        localStorage.removeItem('t')
      },
      setLoading: loading => set({ loading }),
      setUser: user => set({ user }),
      logout: async () => {
        try {
          set({ loading: true })
          const res = await webapi.get('logout').json<
            ResponseType<{
              code: number
            }>
          >()

          if (res.status_code === 1) {
            localStorage.removeItem('t')
            window.location.href = '/login'
            set({
              user: null,
            })
          }
        } catch (error) {
          console.info('login error: ', error)
        } finally {
          set({ loading: false })
        }
      },
      login: async (name, password) => {
        if (!name || !password) {
          toast.error('请输入用户名和密码')
          return
        }
        set({ loading: true, user: null })
        try {
          const { getUserInfo } = get()
          const res = await webapi
            .post('login', {
              json: {
                name,
                password,
              },
            })
            .json<{
              status_code: number
              message: string
              data: {
                access_token: string
              }
            }>()

          if (res.status_code === 1) {
            const { access_token } = res.data
            const url = new URL(window.location.href)
            const redirect = url.searchParams.get('redirect')
            setLocalToken(access_token)
            await getUserInfo()
            toast.success('登录成功，正在跳转页面...')

            if (redirect) {
              window.location.replace(decodeURIComponent(redirect))
            } else {
              window.location.replace('/')
            }
          } else {
            toast.error(res.message)
          }
        } catch (error) {
          console.error(error)
          toast.error('登录失败，请检查邮箱和密码是否正确')
        } finally {
          set({ loading: false })
        }
      },
      changePassword: async (oldPassword, newPassword) => {
        try {
          set({ loading: true })
          const res = await webapi
            .post('user/reset/password', {
              json: {
                old_password: oldPassword,
                password: newPassword,
                password_confirmation: newPassword,
              },
            })
            .json<
              ResponseType<{
                code: number
              }>
            >()

          if (res.status_code === 1) {
            toast.success('密码修改成功，正在跳转登录页面...')
            setTimeout(() => {
              localStorage.removeItem('t')
              window.location.href = '/login'
              set({
                user: null,
                credit: null,
              })
            }, 2000)
          } else {
            toast.error(res.message)
          }
        } catch (error) {
          console.error(error)
          toast.error('修改失败，请稍后重试')
        } finally {
          set({ loading: false })
        }
      },
      getUserInfo: async () => {
        try {
          set({ userLoading: true })
          const res = await webapi
            .get('user/info')
            .json<ResponseType<UserType>>()
          const { data, status_code, message } = res

          if (status_code === 1) {
            set({ user: data })
          } else {
            toast.error(message)
          }
        } catch (error) {
          console.error(error)
          toast.error('获取用户信息失败，请稍后重试')
        } finally {
          set({ userLoading: false })
        }
      },
      getCredits: async () => {
        try {
          set({
            creditLoading: true,
            credit: null,
          })
          const res = await webapi
            .get('my/credits')
            .json<ResponseType<CreditType>>()
          const { data, status_code, message } = res

          if (status_code === 1) {
            set({ credit: data })
          } else {
            toast.error(message)
          }
        } catch (error) {
          console.error(error)
          toast.error('获取积分失败，请稍后重试')
        } finally {
          set({ creditLoading: false })
        }
      },
    }),
    {
      name: 'common',
      storage: createJSONStorage(() => localStorage),
    }
  )
)
